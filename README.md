# APK Automation Testing Framework

This project provides a comprehensive automation testing framework for Android APK files using **Appium** and **WebDriverIO**. It's designed for black-box testing scenarios where you only have the APK file and no access to the source code.

## 🚀 Features

- **Black-box testing** - Test APK files without source code access
- **Page Object Model** - Organized and maintainable test structure
- **Comprehensive test coverage** - Basic functionality, UI interactions, performance, accessibility
- **Automatic screenshots** - Visual verification of test execution
- **Detailed reporting** - JSON reports with app analysis data
- **Cross-platform support** - Works with Android emulators and real devices

## 📋 Prerequisites

Before running the tests, ensure you have the following installed:

### 1. Node.js and npm
```bash
# Check if Node.js is installed
node --version
npm --version
```

### 2. Java Development Kit (JDK)
```bash
# Check if Java is installed
java -version
javac -version
```

### 3. Android SDK
- Install Android Studio or Android SDK Command Line Tools
- Set `ANDROID_HOME` environment variable
- Add Android SDK tools to your PATH

### 4. Appium
```bash
# Install Appium globally
npm install -g appium

# Install UiAutomator2 driver
appium driver install uiautomator2

# Verify installation
appium --version
```

### 5. Android Device/Emulator
- **For Emulator**: Create and start an Android Virtual Device (AVD)
- **For Real Device**: Enable USB Debugging and connect via USB

## 🛠️ Setup Instructions

### 1. Clone/Download the Project
```bash
# If you have the project files
cd apk-automation
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Configure Your APK
- Place your APK file in the project root directory
- Update the APK path in `wdio.conf.js` if needed:
```javascript
'appium:app': path.join(process.cwd(), 'your-app-name.apk'),
```

### 4. Configure Device Settings
Edit `wdio.conf.js` to match your device/emulator:

**For Android Emulator:**
```javascript
'appium:deviceName': 'Android Emulator',
'appium:platformVersion': '11.0', // Your emulator's Android version
```

**For Real Device:**
```javascript
'appium:deviceName': 'Your Device Name',
'appium:platformVersion': '11.0', // Your device's Android version
'appium:udid': 'your-device-udid', // Uncomment and set device UDID
```

### 5. Get Device UDID (for real devices)
```bash
adb devices
```

## 🏃‍♂️ Running Tests

### Start Android Device/Emulator
Make sure your Android device or emulator is running and connected:
```bash
# Check connected devices
adb devices
```

### Run All Tests
```bash
npm test
```

### Run Specific Test Suites
```bash
# Run only basic tests
npx wdio wdio.conf.js --spec ./test/specs/app.basic.test.js

# Run only advanced tests
npx wdio wdio.conf.js --spec ./test/specs/app.advanced.test.js
```

### Start Appium Server Manually (Optional)
```bash
# Start Appium server in a separate terminal
appium

# Then run tests
npm test
```

## 📁 Project Structure

```
apk-automation/
├── app-dev-release.apk          # Your APK file
├── wdio.conf.js                 # WebDriverIO configuration
├── package.json                 # Project dependencies and scripts
├── test/
│   ├── specs/                   # Test specification files
│   │   ├── app.basic.test.js    # Basic functionality tests
│   │   └── app.advanced.test.js # Advanced testing scenarios
│   ├── pageobjects/             # Page Object Model files
│   │   ├── BasePage.js          # Base page with common methods
│   │   └── AppPage.js           # App-specific page object
│   └── helpers/                 # Utility functions
│       └── TestUtils.js         # Test helper methods
├── screenshots/                 # Generated screenshots (created during tests)
├── test-results/               # Test analysis results (created during tests)
└── README.md                   # This file
```

## 🧪 Test Coverage

### Basic Tests (`app.basic.test.js`)
- ✅ App launch verification
- ✅ UI elements detection
- ✅ Basic interactions (swipe, tap, back button)
- ✅ Orientation changes
- ✅ Comprehensive app analysis
- ✅ Navigation flow testing

### Advanced Tests (`app.advanced.test.js`)
- ✅ Form input validation
- ✅ Scrolling and list interactions
- ✅ Performance and responsiveness
- ✅ App state management
- ✅ Accessibility features testing

## 📊 Test Results

### Screenshots
- Automatically captured during test execution
- Saved in `screenshots/` directory
- Named with test context and timestamp

### Analysis Reports
- Detailed app analysis saved as JSON files
- Located in `test-results/` directory
- Include device info, UI elements, and performance metrics

## 🔧 Customization

### Adding New Tests
1. Create new test files in `test/specs/`
2. Follow the existing pattern using Page Object Model
3. Use `TestUtils` for common operations

### Modifying Page Objects
1. Update `test/pageobjects/AppPage.js` with your app's specific elements
2. Use element inspection tools to find correct selectors
3. Add app-specific methods for complex interactions

### Element Selectors
The framework uses multiple selector strategies:
- XPath: `//android.widget.Button[@text="Login"]`
- UiSelector: `android=new UiSelector().textContains("Login")`
- Accessibility ID: `~login-button`

## 🐛 Troubleshooting

### Common Issues

**1. App doesn't install/launch**
- Check APK path in `wdio.conf.js`
- Verify device/emulator is running
- Ensure APK is compatible with device Android version

**2. Elements not found**
- Use `appium-inspector` to identify correct selectors
- Update selectors in page objects
- Check if app UI has loaded completely

**3. Appium connection issues**
- Verify Appium server is running
- Check device connection: `adb devices`
- Restart Appium server and device

**4. Permission errors**
- Enable USB debugging on real devices
- Grant necessary permissions when prompted
- Check ADB connection

### Debug Mode
Add debug information to your tests:
```javascript
// Take screenshot for debugging
await AppPage.takeScreenshot('debug_point');

// Log current activity
console.log(await browser.getCurrentActivity());

// Get page source for element inspection
console.log(await browser.getPageSource());
```

## 📚 Additional Resources

- [Appium Documentation](http://appium.io/docs/en/about-appium/intro/)
- [WebDriverIO Documentation](https://webdriver.io/)
- [Android UI Testing Guide](https://developer.android.com/training/testing/ui-testing)
- [Appium Inspector](https://github.com/appium/appium-inspector)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your improvements
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

---

**Happy Testing! 🎉**

For questions or issues, please check the troubleshooting section or create an issue in the project repository.
