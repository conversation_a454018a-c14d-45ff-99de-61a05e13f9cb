# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test Results
screenshots/
test-results/
allure-results/
allure-report/

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/

# APK files (uncomment if you don't want to track APK files)
# *.apk

# Appium logs
appium.log
